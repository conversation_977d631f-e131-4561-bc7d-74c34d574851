// ============================================================================
// Electron IPC 类型定义
// ============================================================================

/**
 * 主进程API方法定义
 * 在这里定义所有可以从渲染进程调用的主进程方法
 */
export interface MainProcessAPI {
	// 系统信息相关
	getSystemInfo: () => Promise<SystemInfo>
	getAppVersion: () => Promise<string>

	// 窗口管理
	minimizeWindow: () => Promise<void>
	maximizeWindow: () => Promise<void>
	closeWindow: () => Promise<void>

	// 文件操作
	selectFile: (options?: FileSelectOptions) => Promise<string | null>
	selectDirectory: (options?: DirectorySelectOptions) => Promise<string | null>
	readFile: (filePath: string) => Promise<string>
	writeFile: (filePath: string, content: string) => Promise<void>

	// 应用设置
	getSettings: () => Promise<AppSettings>
	updateSettings: (settings: Partial<AppSettings>) => Promise<void>

	// 网络请求
	httpRequest: (options: HttpRequestOptions) => Promise<HttpResponse>

	// 配置管理相关
	getAppPath: () => Promise<string>
	getPackagePath: () => Promise<string>
	getAssetsPath: () => Promise<string>
	getWindowAdapter: () => Promise<any>

	// 窗口工厂相关
	openMainWindow: (options: WindowFactoryOptions) => Promise<string>
	closeCustomWindow: (windowId: string) => Promise<void>
	moveWindowToLeft: (windowId?: string) => Promise<void>
	moveWindowToRight: (windowId?: string) => Promise<void>
	getActiveWindowId: () => Promise<string | null>

	// 更新器相关
	startUpdater: (dirname: string) => Promise<string>
	closeUpdater: (updaterId: string) => Promise<void>
	getUpdaterWindow: (updaterId: string) => Promise<any>

	// 浏览器代理功能
	getTRTCSdkPath: () => Promise<string>
	getTRTCLogPath: () => Promise<string>
	openDevTools: () => Promise<void>

	// 包管理器功能
	getLocalPackageVersion: (pack: string) => Promise<any>
	getServerPackageVersion: (url: string) => Promise<any>
	isUpdateAvailable: (options: IsUpdateAvailableOptions) => Promise<IsUpdateAvailableResponse>
	startDownloadTask: (options: DownloadTaskOptions) => Promise<any>
	abortDownloadTask: (identity: string) => Promise<void>
	decompressZip: (options: { pack: string; file: string }) => Promise<boolean>

	// 页面管理功能
	openWindow: (options: { pack: string; data: any }) => Promise<void>
}

/**
 * 主进程事件定义
 * 定义主进程可以向渲染进程发送的事件
 */
export interface MainProcessEvents {
	// 应用生命周期事件
	'app-ready': void
	'app-will-quit': void

	// 系统事件
	'system-theme-changed': 'light' | 'dark'
	'network-status-changed': NetworkStatus

	// 应用设置事件
	'settings-updated': AppSettings

	// 自定义通知
	notification: NotificationData
}

/**
 * 渲染进程事件定义
 * 定义渲染进程可以向主进程发送的事件
 */
export interface RendererProcessEvents {
	// 用户操作事件
	'user-action': UserActionData
	'page-loaded': PageLoadData
	'error-occurred': ErrorData
}

// ============================================================================
// 数据类型定义
// ============================================================================

export interface SystemInfo {
	platform: string
	arch: string
	version: string
	totalMemory: number
	freeMemory: number
	cpuCount: number
}

export interface FileSelectOptions {
	title?: string
	defaultPath?: string
	filters?: Array<{
		name: string
		extensions: string[]
	}>
	properties?: Array<'openFile' | 'multiSelections' | 'showHiddenFiles'>
}

export interface DirectorySelectOptions {
	title?: string
	defaultPath?: string
	properties?: Array<'openDirectory' | 'createDirectory' | 'showHiddenFiles'>
}

export interface AppSettings {
	theme: 'light' | 'dark' | 'auto'
	language: string
	autoStart: boolean
	notifications: boolean
	[key: string]: any
}

export interface HttpRequestOptions {
	url: string
	method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
	headers?: Record<string, string>
	body?: any
	timeout?: number
}

export interface HttpResponse {
	status: number
	statusText: string
	headers: Record<string, string>
	data: any
}

export interface UpdateInfo {
	version: string
	releaseNotes: string
	downloadUrl: string
	size: number
}

export interface NetworkStatus {
	online: boolean
	type: 'wifi' | 'ethernet' | 'cellular' | 'unknown'
}

export interface NotificationData {
	title: string
	body: string
	type?: 'info' | 'success' | 'warning' | 'error'
	actions?: Array<{
		id: string
		label: string
	}>
}

export interface ProgressData {
	id: string
	progress: number // 0-100
	status: 'running' | 'completed' | 'error'
	message?: string
}

export interface UserActionData {
	action: string
	data?: any
	timestamp: number
}

export interface PageLoadData {
	url: string
	title: string
	loadTime: number
}

export interface ErrorData {
	message: string
	stack?: string
	code?: string
	timestamp: number
}

export interface WindowFactoryOptions {
	pack: string
	data?: WindowSizeData
	unique?: boolean
	needSystemInfo?: boolean
}

export interface WindowSizeData {
	size?: { width: number; height: number }
	ratio?: number
	noFrame?: boolean
	transparent?: boolean
	remoteUrl?: string
}

export interface DownloadTaskOptions {
	pack: string
	url: string
	md5?: string
	version: string
	autoUnzip?: boolean
	checksum?: boolean
	taskId: string
}
export interface IsUpdateAvailableOptions {
	url: string
	pack: string
	checkVersionOnly?: boolean
}

export interface IsUpdateAvailableResponse {
	hasUpdate: boolean
	localVersion?: string
	serverVersion?: string
	available?: boolean
	error?: string
	md5?: string
	url?: string
}

// ============================================================================
// IPC 通信类型
// ============================================================================

/**
 * IPC 请求消息格式
 */
export interface IPCRequest<T = any> {
	id: string
	method: keyof MainProcessAPI
	args: T
	timestamp: number
}

/**
 * IPC 响应消息格式
 */
export interface IPCResponse<T = any> {
	id: string
	success: boolean
	data?: T
	error?: {
		message: string
		code?: string
		stack?: string
	}
	timestamp: number
}

/**
 * IPC 事件消息格式
 */
export interface IPCEvent<T = any> {
	event: string
	data: T
	timestamp: number
}

// ============================================================================
// 客户端API类型
// ============================================================================

/**
 * 渲染进程中可用的Electron API
 */
export interface ElectronAPI {
	/**
	 * 调用主进程方法
	 */
	invoke<K extends keyof MainProcessAPI>(
		method: K,
		...args: Parameters<MainProcessAPI[K]>
	): ReturnType<MainProcessAPI[K]>

	/**
	 * 监听主进程事件
	 */
	on<K extends keyof MainProcessEvents>(
		event: K,
		listener: (data: MainProcessEvents[K]) => void
	): () => void

	/**
	 * 移除事件监听器
	 */
	off<K extends keyof MainProcessEvents>(
		event: K,
		listener?: (data: MainProcessEvents[K]) => void
	): void

	/**
	 * 向主进程发送事件
	 */
	emit<K extends keyof RendererProcessEvents>(event: K, data: RendererProcessEvents[K]): void

	/**
	 * 一次性监听事件
	 */
	once<K extends keyof MainProcessEvents>(
		event: K,
		listener: (data: MainProcessEvents[K]) => void
	): void
}

// ============================================================================
// 全局类型声明
// ============================================================================

declare global {
	interface Window {
		electronAPI: ElectronAPI
	}
}

import { EventEmitter } from 'events'
import fs from 'fs-extra'
import path from 'path'
import type { CancelableRequest, Response, Progress } from 'got'
import { ParallelHasher } from 'ts-md5'

export interface DownloadOptions {
	package_name: string
	cache_path: string
	url: string
	md5?: string
	taskId: string
}

export interface DownloadProgress {
	total: number
	transferred: number
	percent: number
}


export class Downloader extends EventEmitter {
	private package_name: string
	private readonly cache_path: string
	private url: string
	private md5?: string
	readonly taskId: string
	private downloadTask?: CancelableRequest<Response<Buffer>>

	constructor(options: DownloadOptions) {
		super()
		this.package_name = options.package_name
		this.cache_path = options.cache_path
		this.url = options.url
		this.md5 = options.md5
		this.taskId = options.taskId
	}

	async start(): Promise<void> {
		try {
			// Dynamically import got to ensure we get the latest version
			const { got } = await import('got')

			// Ensure cache directory exists
			await fs.ensureDir(this.cache_path)

			const save_path = path.join(this.cache_path, this.package_name)
			console.log('save path: ', save_path)
			// Create the download task
			this.downloadTask = got(this.url, {
				timeout: { socket: 60000 },
				responseType: 'buffer',
				throwHttpErrors: false,
				retry: {
					limit: 1
				}
			})
			if (!this.downloadTask) {
				throw new Error('Failed to create download task')
			}

			this.bindListener(this.downloadTask)

			// Wait for the download to complete and save to file
			const response = await this.downloadTask

			if (response.statusCode >= 400) {
				const error = new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`)
				this.emit('error', error)
				console.error(error)
				return
			}

			await fs.outputFile(save_path, response.body)

			if (this.md5) {
				const checkResult = await this.checkMD5(save_path, this.md5)
				if (!checkResult) {
					const error = new Error('MD5 check failed')
					this.emit('error', error)
					console.error(error)
					return
				}
			}

			this.emit('complete', { path: save_path })
		} catch (error) {
			this.emit('error', error)
			console.error(error)
		}
	}

	private async checkMD5(filePath: string, md5: string): Promise<boolean> {
		if (md5) {
			const fileBlob = fs.readFileSync(filePath, 'binary')
			const md5_worker_path = path.join(__dirname, 'ts-md5/dist/md5_worker.js')
			const hasher = new ParallelHasher(md5_worker_path)
			const fileMD5 = await hasher.hash(fileBlob)
			console.log('md5 value:', fileMD5)
			return fileMD5 == this.md5
		}

		return true
	}

	cancel(): boolean {
		if (this.downloadTask && !this.downloadTask.isCanceled) {
			this.downloadTask.cancel()
			return this.downloadTask.isCanceled
		}
		return true
	}

	private bindListener(task: CancelableRequest<Response<Buffer>>): void {
		task.on('downloadProgress', (progress: Progress) => {
			const downloadProgress: DownloadProgress = {
				total: progress.total || 0,
				transferred: progress.transferred,
				percent: progress.percent || 0
			}

			this.emit('progress', downloadProgress)
		})
	}
}
